package com.example.repairorderapp.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import com.example.repairorderapp.RepairOrderApp
import com.example.repairorderapp.data.model.CosBucketInfo
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.transfer.TransferState
import com.tencent.cos.xml.transfer.TransferStateListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * COS 云存储工具类
 * 提供统一的文件上传功能
 */
object CosUtils {
    private const val TAG = "CosUtils"
    
    /**
     * 单个图片上传结果
     */
    data class UploadResult(
        val url: String,     // 图片访问URL
        val key: String      // COS对象键
    )
    
    /**
     * 上传单个图片到COS
     * @param context 上下文
     * @param uri 图片Uri
     * @param directory 存储目录（如"customer_business/", "repair_report/"等）
     * @param maxSize 图片最大尺寸（默认1024px）
     * @param quality 图片压缩质量（0-100，默认80）
     * @return UploadResult 上传结果，包含url和key
     * @throws Exception 上传过程中的异常
     */
    suspend fun uploadImage(
        context: Context,
        uri: Uri,
        directory: String,
        maxSize: Int = 1024,
        quality: Int = 80
    ): UploadResult = withContext(Dispatchers.IO) {
        // 检查COS是否已初始化，如果没有则尝试初始化
        ensureCosInitialized()

        val tm = RepairOrderApp.transferManager
        val bucketInfo = RepairOrderApp.cosBucketInfo

        if (tm == null || bucketInfo == null) {
            val errorMsg = if (tm == null) "TransferManager 未初始化" else "COS Bucket 信息未获取"
            Log.e(TAG, errorMsg)
            throw Exception("图片上传服务配置错误: $errorMsg")
        }
        
        var inputStream = context.contentResolver.openInputStream(uri)
        var tempFile: File? = null
        
        try {
            if (inputStream == null) {
                throw IOException("无法打开图片流: URI=$uri")
            }
            
            // 1. 获取并压缩图片
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            
            // 先获取图片尺寸
            BitmapFactory.decodeStream(inputStream, null, options)
            inputStream.close()
            
            // 重新打开流
            inputStream = context.contentResolver.openInputStream(uri)
            if (inputStream == null) {
                throw IOException("无法重新打开图片流: URI=$uri")
            }
            
            // 计算压缩比例
            val width = options.outWidth
            val height = options.outHeight
            
            if (width <= 0 || height <= 0) {
                throw IOException("无效的图片尺寸: ${width}x${height}")
            }
            
            // 计算采样率以减少内存使用
            val sampleSize = calculateInSampleSize(options, maxSize, maxSize)
            
            // 使用采样率解码图片
            val decodingOptions = BitmapFactory.Options().apply {
                inSampleSize = sampleSize
                inPreferredConfig = Bitmap.Config.ARGB_8888
            }
            
            val bitmap = BitmapFactory.decodeStream(inputStream, null, decodingOptions)
            inputStream.close()
            inputStream = null
            
            if (bitmap == null) {
                throw IOException("无法解码图片: URI=$uri")
            }
            
            // 计算缩放比例
            val scale = Math.min(maxSize.toFloat() / bitmap.width, maxSize.toFloat() / bitmap.height)
            val newWidth = (bitmap.width * scale).toInt()
            val newHeight = (bitmap.height * scale).toInt()
            
            // 缩放图片
            val scaledBitmap = if (scale < 1) {
                Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
            } else {
                bitmap // 如果图片已经小于最大尺寸，不需要缩放
            }
            
            // 2. 将Bitmap转换为字节数组
            val byteArrayOutputStream = ByteArrayOutputStream()
            scaledBitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArrayOutputStream)
            val compressedBytes = byteArrayOutputStream.toByteArray()
            
            // 如果是不同的bitmap实例，回收原始bitmap
            if (scaledBitmap !== bitmap) {
                bitmap.recycle()
            }
            
            // 3. 将压缩后的字节流写入临时文件
            val uniqueId = UUID.randomUUID().toString()
            tempFile = File.createTempFile("upload_${uniqueId}", ".jpg", context.cacheDir)
            FileOutputStream(tempFile).use { it.write(compressedBytes) }
            
            // 4. 生成日期路径
            val sdf = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
            val datePath = sdf.format(Date())
            
            // 5. 创建对象键(ObjectKey)
            val cosPath = "${bucketInfo.prefix}${directory}${datePath}/${uniqueId}.jpg"
            
            // 6. 执行上传并等待结果
            return@withContext suspendCancellableCoroutine<UploadResult> { continuation ->
                val uploadTask = tm.upload(bucketInfo.bucket, cosPath, tempFile.absolutePath, null)
                
                // 设置上传状态监听
                uploadTask.setTransferStateListener(object : TransferStateListener {
                    override fun onStateChanged(state: TransferState?) {
                        Log.d(TAG, "图片上传状态: $state")
                    }
                })
                
                // 设置结果监听
                uploadTask.setCosXmlResultListener(object : CosXmlResultListener {
                    override fun onSuccess(request: CosXmlRequest?, result: CosXmlResult?) {
                        val url = "https://${bucketInfo.bucket}.cos.${bucketInfo.region}.myqcloud.com/$cosPath"
                        Log.i(TAG, "图片上传成功: $url")
                        tempFile.delete() // 上传成功后删除临时文件
                        
                        continuation.resume(UploadResult(url, cosPath))
                    }
                    
                    override fun onFail(request: CosXmlRequest?, clientException: CosXmlClientException?, serviceException: CosXmlServiceException?) {
                        val errorMsg = when {
                            clientException != null -> "客户端错误: ${clientException.message}"
                            serviceException != null -> "服务端错误: ${serviceException.message}"
                            else -> "未知错误"
                        }
                        Log.e(TAG, "图片上传失败: $errorMsg")
                        tempFile.delete() // 上传失败也删除临时文件
                        
                        continuation.resumeWithException(Exception(errorMsg))
                    }
                })
                
                // 处理取消
                continuation.invokeOnCancellation {
                    try {
                        uploadTask.cancel()
                        tempFile.delete()
                    } catch (e: Exception) {
                        Log.e(TAG, "取消上传任务时出错", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "上传图片到COS时出错", e)
            inputStream?.close()
            tempFile?.delete()
            throw e
        }
    }
    
    /**
     * 计算合适的采样率
     */
    private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1
        
        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 上传多张图片到COS
     * @param context 上下文
     * @param uris 图片Uri列表
     * @param directory 存储目录（如"customer_business/", "repair_report/"等）
     * @param maxSize 图片最大尺寸（默认1024px）
     * @param quality 图片压缩质量（0-100，默认80）
     * @return List<UploadResult> 上传结果列表
     * @throws Exception 上传过程中的异常
     */
    suspend fun uploadImages(
        context: Context,
        uris: List<Uri>,
        directory: String,
        maxSize: Int = 1024,
        quality: Int = 80
    ): List<UploadResult> = withContext(Dispatchers.IO) {
        val results = mutableListOf<UploadResult>()
        val errorMessages = mutableMapOf<Int, String>()
        val successCount = AtomicInteger(0)
        val failureCount = AtomicInteger(0)
        val totalCount = uris.size
        
        if (uris.isEmpty()) {
            return@withContext emptyList<UploadResult>()
        }
        
        return@withContext suspendCancellableCoroutine { continuation ->
            // 处理上传结果
            val handleResult = { index: Int, result: Result<UploadResult> ->
                synchronized(results) {
                    result.fold(
                        onSuccess = { uploadResult ->
                            results.add(uploadResult)
                            successCount.incrementAndGet()
                        },
                        onFailure = { error ->
                            errorMessages[index] = "图片 ${index + 1} 上传失败: ${error.message}"
                            failureCount.incrementAndGet()
                        }
                    )
                    
                    // 检查是否所有上传都已完成
                    if (successCount.get() + failureCount.get() == totalCount) {
                        if (failureCount.get() == 0) {
                            // 全部成功
                            continuation.resume(results)
                        } else {
                            // 有失败的情况
                            val firstErrorIndex = errorMessages.keys.minOrNull() ?: -1
                            val firstErrorMsg = errorMessages[firstErrorIndex] ?: "未知上传错误"
                            continuation.resumeWithException(Exception(firstErrorMsg))
                        }
                    }
                }
            }
            
            // 开始上传每张图片
            uris.forEachIndexed { index, uri ->
                GlobalScope.launch(Dispatchers.IO) {
                    try {
                        val result = uploadImage(context, uri, directory, maxSize, quality)
                        handleResult(index, Result.success(result))
                    } catch (e: Exception) {
                        handleResult(index, Result.failure(e))
                    }
                }
            }
        }
    }
    
    /**
     * 获取COS存储桶信息
     * @return CosBucketInfo? 存储桶信息，如果未初始化则返回null
     */
    fun getBucketInfo(): CosBucketInfo? {
        return RepairOrderApp.cosBucketInfo
    }
    
    /**
     * 检查COS服务是否已初始化
     * @return Boolean 是否已初始化
     */
    fun isInitialized(): Boolean {
        return RepairOrderApp.transferManager != null && RepairOrderApp.cosBucketInfo != null
    }
    
    /**
     * 构建COS对象的完整URL
     * @param cosPath COS对象路径
     * @return String 完整的URL
     * @throws IllegalStateException 如果COS服务未初始化
     */
    fun buildCosUrl(cosPath: String): String {
        val bucketInfo = RepairOrderApp.cosBucketInfo
            ?: throw IllegalStateException("COS Bucket 信息未获取")

        return "https://${bucketInfo.bucket}.cos.${bucketInfo.region}.myqcloud.com/$cosPath"
    }

    /**
     * 确保COS已初始化
     * 如果未初始化且用户已登录，则尝试初始化
     */
    private suspend fun ensureCosInitialized() {
        if (RepairOrderApp.transferManager == null || RepairOrderApp.cosBucketInfo == null) {
            Log.d(TAG, "COS未初始化，尝试初始化...")
            RepairOrderApp.instance.initCosAfterLogin()

            // 等待初始化完成，最多等待5秒
            var retryCount = 0
            while ((RepairOrderApp.transferManager == null || RepairOrderApp.cosBucketInfo == null) && retryCount < 50) {
                delay(100)
                retryCount++
            }

            if (RepairOrderApp.transferManager == null || RepairOrderApp.cosBucketInfo == null) {
                Log.e(TAG, "COS初始化超时或失败")
            } else {
                Log.d(TAG, "COS初始化成功")
            }
        }
    }
}