package com.example.repairorderapp

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import com.example.repairorderapp.data.api.ApiClient
import com.example.repairorderapp.data.api.ApiResponse
import com.example.repairorderapp.data.api.WorkOrderApi
import com.example.repairorderapp.data.model.CosBucketInfo
import com.example.repairorderapp.data.model.CosCredentials
import com.tencent.cos.xml.CosXmlService
import com.tencent.cos.xml.CosXmlServiceConfig
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.transfer.TransferConfig
import com.tencent.cos.xml.transfer.TransferManager
import com.tencent.qcloud.core.auth.BasicLifecycleCredentialProvider
import com.tencent.qcloud.core.auth.QCloudLifecycleCredentials
import com.tencent.qcloud.core.auth.SessionQCloudCredentials
import com.tencent.qcloud.core.auth.QCloudSigner
import com.tencent.qcloud.core.common.QCloudClientException
import kotlinx.coroutines.*
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.lang.ref.WeakReference
import java.util.concurrent.TimeUnit
import java.io.File
import java.io.FileWriter
import java.io.PrintWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 应用程序类
 * 提供全局访问点和应用程序级功能
 */
class RepairOrderApp : MultiDexApplication() {
    
    companion object {
        private const val TAG = "RepairOrderApp"
        
        lateinit var instance: RepairOrderApp
            private set
        lateinit var appContext: Context
            private set
        var cosService: CosXmlService? = null
            private set
        var cosBucketInfo: CosBucketInfo? = null
            private set
        var transferManager: com.tencent.cos.xml.transfer.TransferManager? = null
            private set
            
        private const val INIT_TIMEOUT_MS = 5000L // 5秒超时
        
        // 标识是否在启动页面，用于控制会话过期弹窗的显示
        @Volatile
        var isInSplashActivity: Boolean = false
            private set

        /**
         * 设置是否在启动页面
         */
        fun setInSplashActivity(inSplash: Boolean) {
            isInSplashActivity = inSplash
            Log.d(TAG, "设置启动页面状态: $inSplash")
        }

        /**
         * 隐藏软键盘并取消焦点
         */
        fun hideKeyboard(activity: Activity) {
            try {
                val imm = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                val currentFocus = activity.currentFocus
                if (currentFocus != null) {
                    imm.hideSoftInputFromWindow(currentFocus.windowToken, 0)
                    currentFocus.clearFocus()
                    android.util.Log.d("RepairOrderApp", "已隐藏软键盘并清除焦点")
                }
            } catch (e: Exception) {
                android.util.Log.e("RepairOrderApp", "隐藏软键盘失败: ${e.message}")
            }
        }

        /**
         * 检查触摸点是否在EditText范围内
         */
        private fun isTouchInsideEditText(view: View, event: MotionEvent): Boolean {
            if (view !is EditText) return false

            val location = IntArray(2)
            view.getLocationOnScreen(location)
            val x = location[0]
            val y = location[1]
            val width = view.width
            val height = view.height

            return event.rawX >= x && event.rawX <= x + width &&
                    event.rawY >= y && event.rawY <= y + height
        }

        /**
         * 递归检查ViewGroup中是否有EditText被触摸
         */
        private fun checkEditTextTouch(viewGroup: ViewGroup, event: MotionEvent): Boolean {
            for (i in 0 until viewGroup.childCount) {
                val child = viewGroup.getChildAt(i)

                when {
                    child is EditText && isTouchInsideEditText(child, event) -> {
                        return true
                    }
                    child is ViewGroup -> {
                        if (checkEditTextTouch(child, event)) {
                            return true
                        }
                    }
                }
            }
            return false
        }

        /**
         * 处理Activity的触摸事件
         */
        fun handleActivityTouch(activity: Activity, event: MotionEvent): Boolean {
            if (event.action == MotionEvent.ACTION_DOWN) {
                try {
                    val rootView = activity.findViewById<View>(android.R.id.content)
                    if (rootView is ViewGroup) {
                        val touchedEditText = checkEditTextTouch(rootView, event)
                        if (!touchedEditText) {
                            // 用户点击了非EditText区域，隐藏键盘
                            hideKeyboard(activity)
                            return true
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("RepairOrderApp", "处理触摸事件失败: ${e.message}")
                }
            }
            return false
        }
    }
    
    private val appScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 当前活动的Activity引用
    private var currentActivity: WeakReference<Activity>? = null
    
    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        // 确保 MultiDex 初始化
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        appContext = applicationContext
        
        // 注册Activity生命周期回调，用于跟踪当前Activity
        registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
        
        // 初始化增强日志系统
        try {
            com.example.repairorderapp.utils.EnhancedLogUtils.initialize(this)
            Log.i(TAG, "增强日志系统初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "初始化增强日志系统失败: ${e.message}")
        }

        // 初始化全局异常处理器（必须在崩溃处理器之前）
        try {
            com.example.repairorderapp.manager.GlobalExceptionHandler.initialize(this)
            Log.i(TAG, "全局异常处理器初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "初始化全局异常处理器失败: ${e.message}")
        }

        // 启动全局异常监控服务
        try {
            val monitorIntent = Intent(this, com.example.repairorderapp.service.GlobalExceptionMonitorService::class.java)
            startService(monitorIntent)
            Log.i(TAG, "全局异常监控服务启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "启动全局异常监控服务失败: ${e.message}")
        }

        // 安装全局崩溃处理器
        try {
            com.example.repairorderapp.manager.CrashHandler.install(this)
            Log.i(TAG, "全局崩溃处理器安装成功")
        } catch (e: Exception) {
            Log.e(TAG, "安装全局崩溃处理器失败: ${e.message}")
        }

        // 注册应用生命周期跟踪器
        try {
            com.example.repairorderapp.manager.AppLifecycleTracker.register(this)

            // 设置崩溃处理器到生命周期跟踪器
            val lifecycleTracker = com.example.repairorderapp.manager.AppLifecycleTracker.getInstance()
            val crashHandler = com.example.repairorderapp.manager.CrashHandler.getInstance(this)
            lifecycleTracker.setCrashHandler(crashHandler)

            Log.i(TAG, "应用生命周期跟踪器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册应用生命周期跟踪器失败: ${e.message}")
        }

        // 初始化设备数据上传管理器
        try {
            com.example.repairorderapp.manager.DeviceDataUploadManager.getInstance(this)
            Log.i(TAG, "设备数据上传管理器初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "初始化设备数据上传管理器失败: ${e.message}")
        }

        // 检查并修复令牌问题
        try {
            com.example.repairorderapp.network.TokenFixer.getInstance(this).fixTokenIfNeeded()
        } catch (e: Exception) {
            Log.e(TAG, "修复令牌时出错: ${e.message}")
        }

        // 注册Activity生命周期回调，实现全局触摸监听
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                // Activity创建时设置全局触摸监听
                setupGlobalTouchListener(activity)
                android.util.Log.d("RepairOrderApp", "为 ${activity.javaClass.simpleName} 设置全局触摸监听")
            }

            override fun onActivityStarted(activity: Activity) {}
            override fun onActivityResumed(activity: Activity) {}
            override fun onActivityPaused(activity: Activity) {}
            override fun onActivityStopped(activity: Activity) {}
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            override fun onActivityDestroyed(activity: Activity) {}
        })

        // COS初始化移到登录成功后执行，避免在未登录状态下调用API

        // 初始化腾讯地图SDK并同意隐私协议
        try {
            // 同意隐私协议（需在初始化前调用）
            com.tencent.tencentmap.mapsdk.maps.TencentMapInitializer.setAgreePrivacy(true)
            Log.i(TAG, "TencentMap SDK 隐私协议已同意")
        } catch (e: Exception) {
            Log.e(TAG, "TencentMap SDK 隐私协议设置失败: ${e.message}")
        }

        // 初始化全局崩溃处理器
        setupCrashHandler()
    }


    /**
     * 为Activity设置全局触摸监听
     */
    private fun setupGlobalTouchListener(activity: Activity) {
        try {
            // 获取Activity的根视图
            val rootView = activity.findViewById<View>(android.R.id.content)

            // 设置触摸监听器
            rootView?.setOnTouchListener { _, event ->
                handleActivityTouch(activity, event)
                false // 返回false以便事件继续传递
            }
        } catch (e: Exception) {
            android.util.Log.e("RepairOrderApp", "设置全局触摸监听失败: ${e.message}")
        }
    }

    /**
     * 获取当前活动的Activity
     * @return 当前活动的Activity，如果没有则返回null
     */
    fun getCurrentActivity(): Activity? {
        val activity = currentActivity?.get()
        
        // 检查Activity是否有效（非空且未结束/销毁）
        if (activity != null && !activity.isFinishing && !activity.isDestroyed) {
            return activity
        }
        
        // 如果当前Activity无效，返回null
        return null
    }
    
    /**
     * Activity生命周期回调，用于跟踪当前活动的Activity
     */
    private val activityLifecycleCallbacks = object : ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            Log.d(TAG, "Activity创建: ${activity.javaClass.simpleName}")
        }

        override fun onActivityStarted(activity: Activity) {
            Log.d(TAG, "Activity开始: ${activity.javaClass.simpleName}")
        }

        override fun onActivityResumed(activity: Activity) {
            Log.d(TAG, "Activity恢复: ${activity.javaClass.simpleName}")
            // 更新当前活动的Activity
            currentActivity = WeakReference(activity)
        }

        override fun onActivityPaused(activity: Activity) {
            Log.d(TAG, "Activity暂停: ${activity.javaClass.simpleName}")
        }

        override fun onActivityStopped(activity: Activity) {
            Log.d(TAG, "Activity停止: ${activity.javaClass.simpleName}")
            // 如果当前记录的Activity就是这个被停止的Activity，则清除引用
            if (currentActivity?.get() === activity) {
                currentActivity = null
            }
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            Log.d(TAG, "Activity保存状态: ${activity.javaClass.simpleName}")
        }

        override fun onActivityDestroyed(activity: Activity) {
            Log.d(TAG, "Activity销毁: ${activity.javaClass.simpleName}")
            // 如果当前记录的Activity就是这个被销毁的Activity，则清除引用
            if (currentActivity?.get() === activity) {
                currentActivity = null
            }
            
            // 如果是SplashActivity被销毁，确保关闭TokenManager的对话框
            if (activity.javaClass.simpleName == "SplashActivity") {
                try {
                    Log.d(TAG, "SplashActivity销毁，关闭TokenManager对话框")
                    com.example.repairorderapp.network.TokenManager.getInstance(applicationContext).dismissSessionExpiredDialog()
                } catch (e: Exception) {
                    Log.e(TAG, "关闭TokenManager对话框时出错: ${e.message}", e)
                }
            }
        }
    }

    /**
     * 检查用户是否已登录
     */
    private fun isUserLoggedIn(): Boolean {
        val tokenPrefs = appContext.getSharedPreferences("token_pref", Context.MODE_PRIVATE)
        val token = tokenPrefs.getString("accessToken", "")
        val isLogin = tokenPrefs.getBoolean("isLogin", false)
        return !token.isNullOrEmpty() && isLogin
    }

    /**
     * 用户登录成功后初始化COS SDK
     * 只有在用户已登录的情况下才会调用COS相关API
     */
    fun initCosAfterLogin() {
        if (!isUserLoggedIn()) {
            Log.w(TAG, "用户未登录，跳过COS初始化")
            return
        }

        if (transferManager != null && cosBucketInfo != null) {
            Log.d(TAG, "COS SDK 已初始化，跳过重复初始化")
            return
        }

        appScope.launch {
            try {
                withTimeout(INIT_TIMEOUT_MS) {
                    initCosSdkAsync()
                }
            } catch (e: Exception) {
                Log.e(TAG, "COS初始化超时或失败: ${e.message}")
                // 初始化失败时使用默认配置
                initDefaultConfig()
            }
        }
    }

    private suspend fun initCosSdkAsync() = withContext(Dispatchers.IO) {
        try {
            val workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
            val response = workOrderApi.getCosBucketInfo().await()

            if (response.isSuccessful && response.body()?.code == 200 && response.body()?.data != null) {
                cosBucketInfo = response.body()!!.data
                val bucketInfo = cosBucketInfo ?: return@withContext

                val serviceConfig = CosXmlServiceConfig.Builder()
                    .setRegion(bucketInfo.region)
                    .isHttps(true)
                    .setDebuggable(true)
                    .builder()

                val credentialProvider = MySessionCredentialProvider(workOrderApi)
                cosService = CosXmlService(appContext, serviceConfig, credentialProvider)

                // 初始化TransferManager
                val transferConfig = com.tencent.cos.xml.transfer.TransferConfig.Builder().build()
                transferManager = com.tencent.cos.xml.transfer.TransferManager(cosService, transferConfig)

                Log.i(TAG, "COS SDK 初始化成功: Region=${bucketInfo.region}, Bucket=${bucketInfo.bucket}")
            } else {
                Log.e(TAG, "获取COS配置失败: ${response.code()}")
                initDefaultConfig()
            }
        } catch (e: Exception) {
            Log.e(TAG, "COS SDK 初始化异常: ${e.message}")
            initDefaultConfig()
        }
    }

    private fun initDefaultConfig() {
        // 使用默认配置初始化
        val serviceConfig = CosXmlServiceConfig.Builder()
            .setRegion("ap-guangzhou") // 默认区域
            .isHttps(true)
            .setDebuggable(true)
            .builder()
            
        val workOrderApi = ApiClient.createService(WorkOrderApi::class.java)
        val credentialProvider = MySessionCredentialProvider(workOrderApi)
        cosService = CosXmlService(appContext, serviceConfig, credentialProvider)
        
        // 初始化TransferManager
        val transferConfig = com.tencent.cos.xml.transfer.TransferConfig.Builder().build()
        transferManager = com.tencent.cos.xml.transfer.TransferManager(cosService, transferConfig)
        
        Log.w(TAG, "使用默认配置初始化COS SDK")
    }
    
    override fun onTerminate() {
        super.onTerminate()
        appScope.cancel() // 取消所有协程
        // 取消注册Activity生命周期回调
        unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks)
    }
    
    // 扩展Retrofit Call的挂起函数
    private suspend fun <T> Call<T>.await(): Response<T> = suspendCancellableCoroutine { continuation ->
        enqueue(object : Callback<T> {
            override fun onResponse(call: Call<T>, response: Response<T>) {
                continuation.resume(response) { /* 忽略取消异常 */ }
            }
            
            override fun onFailure(call: Call<T>, t: Throwable) {
                continuation.resumeWithException(t)
            }
        })
        
        continuation.invokeOnCancellation {
            cancel()
        }
    }

    /**
     * 设置全局崩溃处理器
     */
    private fun setupCrashHandler() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            try {
                // 保存崩溃日志到文件
                saveCrashLogToFile(exception)
                
                // 记录到系统日志
                Log.e(TAG, "应用崩溃", exception)
                
            } catch (e: Exception) {
                Log.e(TAG, "保存崩溃日志失败", e)
            } finally {
                // 调用默认处理器
                defaultHandler?.uncaughtException(thread, exception)
            }
        }
    }
    
    /**
     * 保存崩溃日志到文件
     */
    private fun saveCrashLogToFile(exception: Throwable) {
        try {
            // 创建崩溃日志目录
            val crashLogDir = File(getExternalFilesDir(null), "crash_logs")
            if (!crashLogDir.exists()) {
                crashLogDir.mkdirs()
            }
            
            // 生成日志文件名（包含时间戳）
            val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
            val fileName = "crash_${dateFormat.format(Date())}.log"
            val logFile = File(crashLogDir, fileName)
            
            // 写入崩溃信息
            FileWriter(logFile, true).use { writer ->
                PrintWriter(writer).use { printWriter ->
                    printWriter.println("=== 应用崩溃日志 ===")
                    printWriter.println("时间: ${Date()}")
                    printWriter.println("版本: ${packageManager.getPackageInfo(packageName, 0).versionName}")
                    printWriter.println("版本代码: ${packageManager.getPackageInfo(packageName, 0).versionCode}")
                    printWriter.println("设备型号: ${android.os.Build.MODEL}")
                    printWriter.println("Android版本: ${android.os.Build.VERSION.RELEASE}")
                    printWriter.println("当前Activity: ${currentActivity?.get()?.javaClass?.simpleName ?: "未知"}")
                    printWriter.println("\n=== 异常堆栈 ===")
                    exception.printStackTrace(printWriter)
                    printWriter.println("\n=== 日志结束 ===\n")
                }
            }
            
            Log.i(TAG, "崩溃日志已保存到: ${logFile.absolutePath}")
            
        } catch (e: Exception) {
            Log.e(TAG, "保存崩溃日志时发生错误", e)
        }
    }
}

// 自定义 CredentialProvider 用于获取临时密钥
class MySessionCredentialProvider(private val workOrderApi: WorkOrderApi) : BasicLifecycleCredentialProvider() {

    override fun fetchNewCredentials(): QCloudLifecycleCredentials {
        try {
            // 同步调用获取临时密钥
            val response = workOrderApi.getCosCredentials().execute()

            if (response.isSuccessful && response.body()?.code == 200) {
                val responseData = response.body()?.data
                if (responseData != null) {
                    Log.i("MySessionCredentialProvider", "获取 COS 临时密钥成功, expiredTime=${responseData.expiredTime}")
                    // 使用 SessionQCloudCredentials 实现类
                    return SessionQCloudCredentials(
                        responseData.tmpSecretId,
                        responseData.tmpSecretKey,
                        responseData.token,
                        responseData.expiredTime // expiredTime 是秒为单位的时间戳
                    )
                } else {
                    val errorMsg = "获取 COS 临时密钥失败: 响应成功但数据为空"
                    Log.e("MySessionCredentialProvider", errorMsg)
                    throw QCloudClientException(errorMsg)
                }
            } else {
                val errorMsg = "获取 COS 临时密钥失败: ${response.message()} - ${response.body()?.msg}"
                Log.e("MySessionCredentialProvider", errorMsg)
                throw QCloudClientException(errorMsg)
            }
        } catch (e: Exception) {
            Log.e("MySessionCredentialProvider", "获取 COS 临时密钥异常: ${e.message}", e)
            throw QCloudClientException("获取 COS 临时密钥异常: ${e.message}", e)
        }
    }
} 